<!-- 专家出诊屏 -->
<template>
  <div class="page-container">
    <h1>专家出诊屏</h1>
  </div>
</template>

<script>
import { dataConversion } from "@/utils/index.js";

export default {
  name: "specialistScreen",
  data() {
    return {
      departmentList: [],
    };
  },
  created() {
    this.getDepartmentList();
  },
  methods: {
    // 获取科室列表
    async getDepartmentList() {
      const res = await this.$api.department.queryDepartment();
      console.log("二级科室列表", res);
      if (res.success) {
        console.log("请求成功");
        const department = res.data.Response?.Departments?.Department || [];
        this.departmentList = dataConversion(department);
      } else {
        this.$message.error(res.message);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
}
</style>
